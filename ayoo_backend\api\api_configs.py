from .aws_otp_generator import AWSOtpGenerator
from .test_otp_generator import TestOtpGenerator
import os
from dotenv import load_dotenv

current_env = os.getenv("CURRENT_ENV")
# Load the appropriate .env file
if current_env == "uat":
    uat_path = os.path.join("ayoo_backend", ".env.uat")
    load_dotenv(uat_path)
elif current_env == "dev":
    dev_path = os.path.join("ayoo_backend", ".env.dev")
    load_dotenv(dev_path)
elif current_env == "prod":
    prod_path = os.path.join("ayoo_backend", ".env.prod")
    load_dotenv(prod_path)
elif current_env == "test":
    prod_path = os.path.join("ayoo_backend", ".env.test")
    load_dotenv(prod_path)
else:
    # Default to .env if no environment is specified
    local_path = os.path.join("ayoo_backend", ".env.local")
    load_dotenv(local_path)


SCHEDULAR = os.getenv('SCHEDULAR')
DATABASE_URL = os.getenv('DATABASE_URL')
MONGODB_URL = os.getenv('MONGODB_URL')
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_REGION_NAME = os.getenv('AWS_REGION_NAME')
AWS_BUCKET_NAME = os.getenv('AWS_BUCKET_NAME')
# Use TestOtpGenerator for test environment
if current_env == "test":
    OTP_GENERATOR = TestOtpGenerator()
else:
    OTP_GENERATOR = AWSOtpGenerator(AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
OTP_MAX_RETRIES = int(os.getenv('OTP_MAX_RETRIES'))
OTP_EXPIRY_MINUTES = int(os.getenv('OTP_EXPIRY_MINUTES'))
USER_CACHE_CAPACITY = int(os.getenv('USER_CACHE_CAPACITY'))
ALLOWED_ORIGINS = os.getenv('ALLOWED_ORIGINS')
FILE_EXPIRATION_TIME = int(os.getenv('FILE_EXPIRATION_TIME'))
CCAVENUE_ACCESS_CODE = os.getenv("CCAVENUE_ACCESS_CODE") 
CCAVENUE_MERCHANT_ID = os.getenv("CCAVENUE_MERCHANT_ID")
CCAVENUE_WORKING_KEY = os.getenv("CCAVENUE_WORKING_KEY")

# Google Maps Api
GOOGLE_MAPS_API_KEY = os.getenv('GOOGLE_MAPS_API_KEY')

# Firebase Messaging and Notification Key
FIREBASE_SERVER_TOKEN = os.getenv('FIREBASE_SERVER_TOKEN')

T_url = os.getenv('T_url')
p_redirect_url = os.getenv('p_redirect_url')
p_cancel_url = os.getenv('p_redirect_url')


SECRET_KEY = os.getenv('SECRET_KEY')
ALGORITHM = os.getenv('ALGORITHM')
# ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 365
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv('ACCESS_TOKEN_EXPIRE_MINUTES'))

JITSI_SECRET_KEY = os.getenv('JITSI_SECRET_KEY')
JITSI_ALGORITHM = os.getenv('JITSI_ALGORITHM')
APPLICATION_ID = os.getenv('APPLICATION_ID')

URL_SHORTENER_PATH = os.getenv('URL_SHORTENER_PATH')
WEB_URL_PATH = os.getenv('WEB_URL_PATH')

EXCLUDE_ID = os.getenv('EXCLUDE_ID')
PDF_FONT_FILE_PATH = os.getenv('PDF_FONT_FILE_PATH')

SEND_NOTIFICATION = os.getenv('SEND_NOTIFICATION')
PRIVATE_S3 = os.getenv('PRIVATE_S3')
MEET_URL = os.getenv('MEET_URL')
FEATURE_ENABLE_VITALS = os.getenv('FEATURE_ENABLE_VITALS')

VIRTUAL_SLOTS_FETCH_DAYS = 3 if current_env in ["dev", "uat"] else 7

ZOOM_ACC_ID = os.getenv("ZOOM_ACC_ID")
ZOOM_CLIENT_ID = os.getenv("ZOOM_CLIENT_ID")
ZOOM_SECRET =  os.getenv("ZOOM_SECRET")

VIDEO_SDK_API_KEY = os.getenv("VIDEO_SDK_API_KEY")
VIDEO_SDK_API_SECRET = os.getenv("VIDEO_SDK_API_SECRET")
