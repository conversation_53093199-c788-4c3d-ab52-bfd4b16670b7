from sqlalchemy.orm import scoped_session
from datetime import datetime, timed<PERSON><PERSON>

from ayoo_backend.api.chat_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ayoo_backend.api.chat_models import SaveChatModel
from ayoo_backend.api.dbmodels import DBUser, DBMembershipPlan
from ayoo_backend.api.doctor_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ayoo_backend.api.firebase_controller import FireBaseNotificationController
from ayoo_backend.api.firebase_models import SubscriptionEvent, MedicationEvent, FirebaseMessage, NotificationCategory, \
    NotificationSubCategory
from ayoo_backend.api.patient_models import AppointmentBooking

from ayoo_backend.api.view_controller import UserController
from ayoo_backend.api.views import logger, loggers

from ayoo_backend.api.revenue_controller import Revenuecontroller
from .revenue_models import Paymentsuccess
from ayoo_backend.api.patient_controller import PatientController
from ayoo_backend.api.revenue_models import OrderQueryRequest
from .api_configs import CCAVENUE_ACCESS_CODE, CCAVENUE_WORKING_KEY

import requests

import json
import time

from Crypto.Cipher import AES
import hashlib
import uuid
from fastapi.responses import HTMLResponse

# from ayoo_backend.api.firebase_notification import FireBaseNotification
# notif_ctrl = FireBaseNotification()

from ayoo_backend.api.fire_base_notifications_v1.firebase_notifications_controller_v1 import FireBaseNotificationV1

notif_ctrl = FireBaseNotificationV1()


#     QDM = 'QDM'   # every day morning
#     QDN = 'QDN'   # every day night
#     BD = 'BD'     # twice daily
#     TID = 'TID'   # thrice daily
#     QID = 'QID'   # four times daily
#     AD = 'AD'     # every other day
#     QWK = 'QWK'   # once in a week

class ScheduledTasksController:
    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']

    def __get_all_active_devices(self, user_id: str):
        try:
            return self.mongo_db['UserDeviceInfo'].find(dict(user_id=user_id))
        except Exception as e:
            return None, f'Internal Error code {str(e)} for getting active Device Id for {user_id}'

    def __check_user_or_doctor(self, id: str):
        doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
        user_ctrl = UserController(db=self.db, otp_generator=None)
        user_details = user_ctrl.get_user_by_id(id)
        doctor_details, msg = doctor_ctrl.get_by_id(
            doctorid=id)
        return user_details, doctor_details

    def job(self):
        logger.info('testing cron job')

    def __get_member_by_id(self, userid: str):
        from ayoo_backend.api import dbmodels

        resp: DBUser  # = None
        resp = self.db.query(dbmodels.DBUser).filter(
            DBUser.userid == userid).one_or_none()
        return resp

    def __get_membership_plan_by_id(self, planid: str):
        from ayoo_backend.api import dbmodels

        membership_plan: DBMembershipPlan = self.db.query(
            dbmodels.DBMembershipPlan).filter_by(planid=planid).one_or_none()
        return membership_plan

    def membership_expiry(self):
        try:
            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
            # logger.info("membership exp")
            data = self.mongo_db['ACareSubscription'].find({"valid_till": {"$lte": datetime.now()}, "is_active": True})

            # logger.info(len(list(data.clone())))
            ids_to_notify = []
            if len(list(data.clone())):
                for record in data:
                    ids_to_notify.append(record)
                    self.mongo_db['ACareSubscription'].find_one_and_update(
                        {"_id": record["_id"], "valid_till": {"$lte": datetime.now()}, "is_active": True},
                        {"$set": {"is_active": False}})

            for id in ids_to_notify:

                username = self.__get_member_by_id(id['userid'])
                if username == None:
                    name = 'Hey'
                else:
                    name = username.firstname
                subscription_notif = SubscriptionEvent(subscription_id=id['subscriptionid'],
                                                       user_id=id['userid'],
                                                       user_name=name,
                                                       event_date=datetime.now(),
                                                       plan_id=id['planid'],
                                                       plan_type=self.__get_membership_plan_by_id(
                                                           planid=str(id['planid'])).title,
                                                       valid_till=id['valid_till'],
                                                       is_active=False)
                frbs_ctrl.subscription_expired(subscription_event=subscription_notif)

                #### Remove Family Doctor on Subscription Expiry ####
                family_doctor_exists = self.mongo_db['UserCollection'].find_one(
                    dict(userid=id['userid']))
                # logger.info('family_doctor' in family_doctor_exists)
                if 'family_doctor' in family_doctor_exists:
                    self.mongo_db['UserCollection'].find_one_and_update({"userid": id['userid']},
                                                                        {"$unset": {"family_doctor": ""}})

        except Exception as e:
            return 'Error updating records'

    def membership_expiry_reminder(self):
        try:
            frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
            # logger.info("membership exp reminder")
            days_remaining = (datetime.now() + timedelta(days=10)).replace(hour=0, minute=0, second=0, microsecond=0)
            data = self.mongo_db['ACareSubscription'].find({"valid_till": {"$eq": days_remaining}, "is_active": True})

            # logger.info(len(list(data.clone())))
            ids_to_notify = []
            if len(list(data.clone())):
                for record in data:
                    ids_to_notify.append(record)

            # logger.info(ids_to_notify)

            for id in ids_to_notify:
                username = self.__get_member_by_id(id['userid'])
                if username == None:
                    name = 'Hey'
                else:
                    name = username.firstname

                subscription_notif = SubscriptionEvent(subscription_id=id['subscriptionid'],
                                                       user_id=id['userid'],
                                                       user_name=name,
                                                       event_date=id['valid_till'],
                                                       plan_id=id['planid'],
                                                       plan_type=self.__get_membership_plan_by_id(
                                                           planid=str(id['planid'])).title,
                                                       valid_till=id['valid_till']
                                                       )
                frbs_ctrl.subscription_expiry_reminder(subscription_event=subscription_notif)

        except Exception as e:
            return 'Error updating records'

    def send_notif_today(self, start: datetime, end: datetime, today: datetime, frequency: int):
        temp = start
        while (temp < end):
            if today == start:
                # 'First Day'
                return True
            if today == temp + timedelta(days=frequency):
                return True
            temp = temp + timedelta(days=frequency)
        return False

    def get_primary_care_cases_for_meds_reminder(self):
        raw_data = self.mongo_db['UserCollection'].find({"cases.isOpen": "True"})
        data = list(raw_data.clone())

        notif_data = []
        for entities in data:
            cases = entities['cases']
            for case in cases:
                if case['isOpen'] == 'True' and case['notif_toggle'] == 'True':
                    all_medications = case['prescriptions']['medication']
                    latest_meds = all_medications[len(all_medications) - 1]
                    notif_data.append(dict(userid=entities['userid'], medicines=latest_meds))

        return notif_data

    def get_mental_health_cases_for_meds_reminder(self):
        raw_data = self.mongo_db['UserCollection'].find({"psychiatrist_prescriptions.is_open": True})
        data = list(raw_data.clone())

        notif_data = []
        for entities in data:
            cases = entities['psychiatrist_prescriptions']
            for case in cases:
                if case['is_open'] == True and case['medicine_reminder'] == True:
                    all_medications = case['medications']
                    if 'meds' in all_medications['current_record']:
                        latest_meds = all_medications['current_record']['meds']
                        notif_data.append(dict(userid=entities['userid'], medicines=latest_meds))

        return notif_data

    def all_cases_details_for_meds_reminder(self):
        details = []
        mh_cases = self.get_mental_health_cases_for_meds_reminder()
        details.extend(mh_cases)

        pc_cases = self.get_primary_care_cases_for_meds_reminder()
        details.extend(pc_cases)

        return details

    def meds_reminder(self):

        frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
        time_now = datetime.now().time()
        time_now = time_now.replace(second=0, microsecond=0)
        time_now = time_now.strftime('%H%M')

        notif_data = self.all_cases_details_for_meds_reminder()

        for element in notif_data:
            # #logger.info(element)
            medicine = element['medicines']
            for med in medicine:
                user = self.__get_member_by_id(userid=element['userid'])
                if user:
                    username = user.firstname
                else:
                    username = ''

                compare_date = datetime.now().strftime("%Y-%m-%d")
                start_date = datetime.strptime(med['start_date'], '%Y-%m-%d')
                end_date = datetime.strptime(med['end_date'], '%Y-%m-%d')
                today_date = datetime.strptime(compare_date, '%Y-%m-%d')

                if (compare_date <= med['end_date']) and (compare_date >= med['start_date']):
                    frequency = med['frequency']
                    send_notif_today = True
                    dose = 1
                    in_days = 1

                    # # for frequency of every 'n' days
                    # if frequency.startswith('Q') and frequency.endswith('D') and any(i.isdigit() for i in frequency):
                    #     in_days = int((re.findall('[0-9]+', frequency))[0])

                    if frequency == 'QWK':
                        in_days = 7
                    if frequency == 'AD':
                        in_days = 2

                    if in_days > 1:
                        send_notif_today = self.send_notif_today(start_date, end_date, today_date, in_days)

                    medicine_times = ["0800"]

                    if frequency == 'QDN':
                        medicine_times = ["2000"]
                    if frequency == 'BD':
                        medicine_times = ["0800", "2000"]
                    if frequency == 'TID':
                        medicine_times = ["0800", "1400", "2000"]
                    if frequency == 'QID':
                        medicine_times = ["0800", "1200", "1600", "2000"]
                    if send_notif_today == True:
                        med_notif = MedicationEvent(medicine_id=med['medicine_id'],
                                                    medicine_name=med['medicine'],
                                                    user_id=element['userid'],
                                                    user_name=username,
                                                    dose=dose,
                                                    dose_type="Dose",  # tablet, quantity -> mL
                                                    event_date=datetime.now(),
                                                    remarks='None')

                        # for testing with current time
                        # medicine_times=["1609"]
                        if time_now in medicine_times:
                            frbs_ctrl.medicine_reminder(medication_event=med_notif)

    def ayoo_check_in_days(self):
        try:
            chat_ctrl = ChatController(db=self.db, mongo=self.mongo)
            admin_ids, msg = chat_ctrl.get_ayoo_support_id()
            admin_id = admin_ids[0]['admin_id']

            date_now = datetime.now().date()
            get_check_in_days_data = self.mongo_db['UserCollection'].find({"psychiatrist_prescriptions.is_open": True})

            if len(list(get_check_in_days_data.clone())):
                for record in get_check_in_days_data:
                    if 'psychiatrist_prescriptions' in record:
                        for cases in record['psychiatrist_prescriptions']:
                            if cases['is_open']:
                                if 'ayoo_check_in_days' in cases['follow_up']['current_record']:
                                    follow_up_recorded_date = datetime.date(
                                        cases['follow_up']['current_record']['date_recorded'])
                                    check_in_days = cases['follow_up']['current_record']['ayoo_check_in_days']

                                    if follow_up_recorded_date + timedelta(days=int(check_in_days)) == date_now:
                                        save_chat, msg = chat_ctrl.save_chat_with_ayoo_support(
                                            chat_data=SaveChatModel(user_one_id=admin_id,
                                                                    user_two_id=record['userid'],
                                                                    message='Hello, how are you doing?'))
                                        # #logger.info(save_chat)

        except Exception as e:
            return 'Error updating records'

    def payment_status_check(self):
        try:
            # Step 1: Check for success payments, but failed "activity-confirmation" in db
            # Step 2: Send instant mail, msg and in-app notifs that the payment will be refunded
            # Step 3: Add notif_status fields in corresponding Paymentgateway3 collection records

            get_payment_status = self.mongo_db['Paymentgateway3'].find({
                '$and': [
                    {'$or': [
                        {'payment_status': 'Successful'}, {'payment_status': 'success'}
                    ]},
                    {'is_activity_confirmed': False}
                ]

            })
            success_payment_list = list(get_payment_status.clone())

            for data in success_payment_list:
                send_notification = False

                if 'notification_status' in data:
                    notif_status = data['notification_status']
                    if notif_status['in_app_notif'] == True or notif_status['mobile_msg'] == True or notif_status[
                        'email'] == True:
                        send_notification = False
                    else:
                        send_notification = True
                else:
                    send_notification = True

                if send_notification is True:
                    frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)
                    firebase_message = FirebaseMessage(notif_category=NotificationCategory.Payment,
                                                       sub_category=NotificationSubCategory.PaymentSuccessOperationFailure,
                                                       object_id=data['order_id'],
                                                       event_datetime=data['payment_date'],
                                                       sent_on=datetime.now(),
                                                       sent_to=str(data['user_id']),
                                                       remarks=data['appointment_or_membership']
                                                       )

                    notification_status, msg_status_code, mail_status_code = frbs_ctrl.payment_confirm_and_operation_failure_reminder(
                        firebase_message=firebase_message)

                    self.mongo_db['Paymentgateway3'].find_one_and_update(dict(order_id=data['order_id']), {
                        "$set": dict(notification_status=dict(in_app_notif=notification_status,
                                                              mobile_msg=True if msg_status_code == 200 else False,
                                                              email=True if msg_status_code == 200 else False))
                    })
                # else:
                #     #logger.info('No need to send notif, as it is already sent and delivered')

        except Exception as e:
            return f'Error checking records of payment status: {str(e)}'

    def pad(self, data):
        length = 16 - (len(data) % 16)
        data += chr(length) * length
        return data

    def encrypt(self, plainText, workingKey):
        iv = bytes('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
        plainText = self.pad(plainText)
        res = bytes(plainText, 'utf-8')
        encDigest = hashlib.md5(workingKey.encode('utf-8'))
        # logger.info(encDigest.digest())
        # logger.info(type(encDigest.digest()))
        # logger.info("step 1")
        # encDigest.update(workingKey.encode())
        enc_cipher = AES.new(encDigest.digest(), AES.MODE_CBC, iv)
        # logger.info("step 2")
        encryptedText = enc_cipher.encrypt(res).hex()
        # logger.info("step 3")
        # tr = encryptedText.encode('hex')
        # return tr
        return encryptedText

    def decrypt6(self, cipherText, workingKey):
        iv = bytes('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
        encDigest = hashlib.md5(workingKey.encode('utf-8')).digest()
        dec_cipher = AES.new(encDigest, AES.MODE_CBC, iv)
        decryptedText = dec_cipher.decrypt(bytes.fromhex(cipherText))
        text = decryptedText.rstrip(b'\0').decode('utf-8')
        # logger.info(text)

        # logger.info(type(text))
        # logger.info(len(text))
        # text1 = re.sub(r'[^\x00-\x7F]', '', text)
        text1 = text[:text.rfind('}') + 1]
        # text1=text.rstrip()
        # logger.info(text1)
        # logger.info(len(text1))
        return text1

    def decrypt_order_status(cipherText, workingKey, secret_key):
        try:
            iv = bytes('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
            encDigest = hashlib.md5(workingKey.encode('utf-8')).digest()
            dec_cipher = AES.new(encDigest, AES.MODE_CBC, iv)
            decryptedText = dec_cipher.decrypt(bytes.fromhex(cipherText))
            # decryptedText = dec_cipher.decrypt(cipherText).hex()
            # logger.info("decryptedText !!!!!!!!!!!!!")
            # logger.info(decryptedText)
            text = decryptedText.rstrip(b'\0').decode('utf-8')
            # text = decryptedText.decode('utf-8')

            data_pairs = text.split('&')
            # input(data_pairs)
            return data_pairs
        except Exception as e:
            return None, "Internal Error {}".format(e)

    def Invoice_status(self, invoice_id):
        access_code = CCAVENUE_ACCESS_CODE
        working_key = CCAVENUE_WORKING_KEY
        today_date = datetime.now().strftime('%d-%m-%Y')
        merchant_json_data = {
            "invoice_id": invoice_id,
            "from_date": today_date}
        order_type = 'invoiceList'

        merchant_data_json = json.dumps(merchant_json_data)
        encrypted_data = self.encrypt(merchant_data_json, working_key)
        loggers['logger_invoice_status'].info(f'encrypted data: {encrypted_data}')
        payload = {
            'enc_request': encrypted_data,
            'access_code': access_code,
            'command': order_type,
            'request_type': 'JSON',
            'response_type': 'JSON',
            "version": '1.1'
        }
        response = requests.post("https://api.ccavenue.com/apis/servlet/DoWebTrans", data=payload)
        loggers['logger_invoice_status'].info(f'response: {response}')

        if response.status_code == 200:
            loggers['logger_invoice_status'].info(f'response text: {response.text}')
            encrypted_response = response.text.split('&')[1].split('=')[1]
            loggers['logger_invoice_status'].info(f'\nencrypted_response: {encrypted_response}')
            status = self.decrypt_order_status(encrypted_response, working_key)
            loggers['logger_invoice_status'].info(f'\nstatus: {status}')
            x = json.loads(status)
            loggers['logger_invoice_status'].info(f'\nstatus json resp: {x}')
            return x
        return None

    def Invoice_status1(self):
        try:
            status_list = ["initiate", "Initiate", "initiated", "Initiated", "pending", "Pending"]
            resp1 = list(self.mongo_db['Paymentgateway3'].find(
                {'$or': [{"payment_status": {"$in": status_list}},
                         {"status.status": {"$in": status_list}}
                         ]}
            ))
            loggers['logger11'].info(resp1)
            for res in resp1:
                if "-" in res['order_id']:
                    continue
                x = self.Invoice_status(invoice_id=res['order_id'])

                if 'invoice_List' in x:
                    loggers['logger11'].info(x)
                    self.mongo_db['Paymentgateway3'].find_one_and_update({"order_id": res['order_id']},
                                                                         {'$set': {
                                                                             'payment_status': x['invoice_List'][0][
                                                                                 'invoice_status'],
                                                                             'transaction_id': x['invoice_List'][0][
                                                                                 'reference_no']}})
                    if x['invoice_List'][0]['invoice_status'] in ["Expired", "Aborted", "Cancelled", "Failed",
                                                                  "Awaited", "Pending"]:
                        self.mongo_db['Appointments'].find_one_and_update({"appointment_id": res['appointment_id']}, {
                            "$set": {'status': dict(status='Pending',
                                                    Reason=f'Payment {x["invoice_List"][0]["invoice_status"]}',
                                                    comment=''),
                                     'payment_status': x['invoice_List'][0]['invoice_status'],
                                     'is_active': False,
                                     'is_confirmed': False
                                     }})

                    if x['invoice_List'][0]['invoice_status'] == "Successful":
                        ptn_ctrl = PatientController(db=self.db, mongo=self.mongo)
                        appointment_data = self.mongo_db['Appointments'].find_one(
                            {"appointment_id": res['appointment_id']})
                        patient_id = appointment_data['patient_id']
                        is_first_appointment = ptn_ctrl.manage_first_appointment(patient_id=patient_id)
                        self.mongo_db['Appointments'].find_one_and_update({"appointment_id": res['appointment_id']}, {
                            "$set": {'is_active': True, 'is_confirmed': True,
                                     'status': dict(status='Booked', Reason='', comment=''),
                                     'payment_status': x['invoice_List'][0]['invoice_status'],
                                     'is_first_appointment': is_first_appointment
                                     }})

                        if res['appointment_or_membership'] not in ["Adhoc", "adhoc"]:
                            notif_data_exist = self.mongo_db['Notifications'].find_one(
                                {'object_id': res['appointment_id']})
                            if notif_data_exist is None:
                                ptn_ctrl.manage_notifications_for_booked_appointments(
                                    appointment_id=res['appointment_id'])
                else:
                    loggers['logger11'].info("No response from ccavenue quickbook api for Adhoc")
                    # code added on 30 June 2024 to release the slot after 15 minutes of no response
                    if datetime.now() > res['created_at'] + timedelta(minutes=15):
                        self.mongo_db['Appointments'].find_one_and_update(
                            {'appointment_id': res['appointment_id']}, {
                                '$set': {'status.status': 'Abandoned'}
                            })

        except Exception as e:
            loggers['logger11'].info(str(e))

    def invoice_status_new(self):
        try:
            status_list = ["initiate", "Initiate", "initiated", "Initiated", "pending", "Pending", "Abandoned",
                           "abandoned"]

            time_threshold = datetime.now() - timedelta(hours=48)

            pending_appointments = list(self.mongo_db['Appointments'].find({
                '$and': [
                    {
                        '$or': [
                            {'payment_status': {'$in': status_list}},
                            {'status.status': {'$in': status_list}}
                        ]
                    },
                    {'created_at': {'$gte': time_threshold}}
                ]
            }).clone())

            for appointments in pending_appointments:
                payment_record = self.mongo_db['Paymentgateway3'].find_one({'$and': [
                    {'appointment_id': appointments.get('appointment_id')},
                    {'payment_status': 'Successful'}
                ]})
                if payment_record is not None:
                    ptn_ctrl = PatientController(db=self.db, mongo=self.mongo)
                    is_first_appointment = ptn_ctrl.manage_first_appointment(patient_id=appointments.get('patient_id'))
                    self.mongo_db['Appointments'].find_one_and_update(
                        {'appointment_id': appointments.get('appointment_id')}, {
                            '$set':
                                {'is_active': True,
                                 'is_confirmed': True,
                                 'status': dict(status='Booked',
                                                Reason='',
                                                comment=''),
                                 'payment_status': 'Successful',
                                 'is_first_appointment': is_first_appointment
                                 }})
                    notif_data_exist = self.mongo_db['Notifications'].find_one(
                        {'object_id': appointments.get('appointment_id')})
                    if notif_data_exist is None:
                        ptn_ctrl.manage_notifications_for_booked_appointments(
                            appointment_id=appointments.get('appointment_id'))

        except Exception as e:
            loggers['logger11'].info(str(e))

    def invoice_status_check_for_unreleased_slots(self):
        # function added on 30 June 2024 to release the slot after 15 minutes of no response
        try:
            status_list = ["initiate", "Initiate", "initiated", "Initiated", "pending", "Pending"]
            self.mongo_db['Appointments'].update_many(
                {'$and': [{'status.status': {'$in': status_list}},
                          {'created_at': {
                              '$lte': datetime.now() - timedelta(
                                  minutes=15)}}
                          ]},
                {
                    '$set': {'status.status': 'Abandoned'}
                })
        except Exception as e:
            loggers['logger11'].info(str(e))

    def payment_status_failed(self):
        try:
            # #logger.info("invoice status schedule")
            resp = list(self.mongo_db['Paymentgateway3'].find({"payment_status": "initiate"}))
            for res in resp:
                payment_date = datetime.fromisoformat(str(res['payment_date']))
                if datetime.now() > payment_date + timedelta(minutes=5):
                    if res['appointment_id'] is not None:
                        self.mongo_db['Appointments'].find_one_and_update(
                            {"$and": [{"appointment_id": res['appointment_id']}, {
                                "$or": [{"status.status": "initiate"}, {"status.status": "Initiated"}]
                            }]}, {"$set": {'status': dict(status='Pending', Reason='Payment cancelled', comment=''),
                                           'payment_status': "Initiated",
                                           'is_active': False,
                                           'is_confirmed': False}})
        except Exception as e:
            loggers['logger11'].info(str(e))

    # Cron job for appointment status complete same logic as below can be used
    """def appointment_status_completed(self): 
        try:
            # #logger.info("invoice status schedule")
            resp = list(self.mongo_db['Appointments'].find({"$and":[{"status.status": "Booked"},
                                                           {"end_date":{"$gte":datetime.now() -timedelta(minutes=32),"$lte":datetime.now()}}]}))
            for res in resp:
                   print("update appointment status initiated")
                   self.mongo_db['Appointments'].find_one_and_update({"appointment_id": res['appointment_id']}, {
                            "$set": {'status' : dict( status='Completed', Reason='',comment='')}})
        except Exception as e:
            loggers['logger11'].info(str(e))"""

    def auto_submit_case_sheet(self):
        case_sheet_list = list(self.mongo_db['CaseSheet'].find(
                {'$and': [{'is_case_sheet_submitted': False},
                          {'created_at': {
                              '$lte': datetime.now() - timedelta(days=1)}}
                          ]}).clone())

        from ayoo_backend.api.DAOs.caseSheetDAO import CaseSheetDAO
        for case in case_sheet_list:
            try:
                CaseSheetDAO().submit_case_sheet(appointment_id=case.get('appointment_id'))
            except Exception as e:
                print(case.get('appointment_id'))
                print(f'Error for case : {str(e)}')

