import boto3
from .OtpGenerator import Otp<PERSON>enerator
# from ayoo_backend.api.api_configs import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY
from .views import logger


class AWSOtpGenerator(OtpGenerator):
    def __init__(self, aws_access_key_id=None, aws_secret_access_key=None):
        if not aws_access_key_id:
            # read from env
            self.client = boto3.client(
                "sns",
                region_name='ap-south-1'

            )
            self.email_client = boto3.client(
                "ses",
                region_name='ap-south-1'

            )
        else:
            self.client = boto3.client(
                "sns",
                # aws_access_key_id=aws_access_key_id,
                # aws_secret_access_key=aws_secret_access_key,
                region_name='ap-south-1'
            )
            self.email_client = boto3.client(
                "ses",
                # aws_access_key_id=aws_access_key_id,
                # aws_secret_access_key=aws_secret_access_key,
                region_name='ap-south-1'
            )

    def send_otp(self, transaction_id, otp, mobile=None, email=None):
        message = 'Your otp for Ayoo Care is ' + str(otp)
        # # + ' transactionid = ' + str(transaction_id)
        if not mobile.startswith('+91'):
            resp = self.client.publish(PhoneNumber=mobile, Message=message)

        #logger.info(resp)
        # # for r in resp:
        # #     #logger.info(r)

        if email is not None:
            response = self.email_client.send_email(
                Destination={
                    'ToAddresses': [email],
                },
                Message={
                    'Body': {
                        'Text': {
                            'Charset': 'UTF-8',
                            'Data': message,
                        },
                    },
                    'Subject': {
                        'Charset': 'UTF-8',
                        'Data': 'Ayoo Care OTP',
                    },
                },
                Source='<EMAIL>',
            )
            #logger.info('ses response', response)


if __name__ == '__main__':
    AWS_ACCESS_KEY_ID = '********************'
    AWS_SECRET_ACCESS_KEY = 'NrvlJjcOCELwt8f+CmGhr6I/bWTJp0uv3GqyfiBv'
    o = AWSOtpGenerator(AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
    # o.send_otp('something', '1234', '+917795066613', '<EMAIL>')
    o.send_otp('something', '1234', '+919845540799', '<EMAIL>')
    o.send_otp('something', '1234', '+916202878654', '<EMAIL>')
